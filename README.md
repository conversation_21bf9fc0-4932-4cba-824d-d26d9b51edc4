# Baranof Lodge - Pirate-Themed Website

A modern, responsive website for Baranof Lodge in Sitka, Alaska, featuring an interactive pirate-themed treasure hunting mini-game.

## Features

### Website
- **Pirate Theme**: Brown/blue color scheme inspired by old pirate maps
- **Responsive Design**: Optimized for desktop and mobile devices
- **Modern UI**: Clean, professional layout with pirate aesthetic
- **SEO Optimized**: Proper meta tags and structured data
- **Accessibility**: Keyboard navigation and screen reader friendly

### Navigation
- Book Now button (styled like a glowing vacancy sign)
- Phone number with direct calling link
- Concerns/Contact email link
- Responsive mobile menu

### Interactive Game
- **32x32 Tile-Based World**: Grid system overlaying the map background
- **Character Movement**: WASD/Arrow keys + Space to jump
- **Mobile Controls**: Touch-friendly on-screen controls
- **Item Collection**: Find treasures, trash, and other items
- **Scoring System**: Gas pump-style counting animation
- **Timer**: 2-minute countdown
- **Leaderboard**: Local high scores with player names

### Game Items
**Treasures (100 points each):**
- Footsteps, 3 Shovels, Skull and bones, Small house
- 3 Campfires, Church cross, Fish, Bigfoot foot, Hidden cross

**Trash (50 points each):**
- Ziplock bags, Bottles, Face masks

**Other Items (75 points each):**
- 2 Dragons, 3 Ships, 1 Plane
- Forest, Regular trees, 2 Camp tents, Coffee stain

**Hotels (Interactive):**
- Baranof Lodge, Southeast Resort, Sitka Hotel
- Click to visit booking pages

## Technical Details

### File Structure
```
├── index.html          # Main HTML file
├── css/
│   ├── style.css       # Main website styles
│   └── game.css        # Game-specific styles
├── js/
│   ├── script.js       # Website functionality
│   └── game.js         # Game engine
├── images/
│   ├── hero/           # Map backgrounds
│   ├── logo/           # Lodge logos
│   ├── characters/     # Character sprites
│   └── ...
└── README.md
```

### Technologies Used
- **HTML5**: Semantic markup
- **CSS3**: Modern styling with CSS Grid/Flexbox
- **JavaScript ES6+**: Game engine and website functionality
- **Bootstrap 5**: Responsive framework
- **Font Awesome**: Icons
- **Google Fonts**: Cinzel and Roboto Slab fonts

### Game Engine Features
- Object-oriented design with PirateGame class
- Collision detection system
- Animation system for character sprites
- Local storage for high scores
- Responsive canvas-based rendering
- Touch and keyboard input handling

## Setup Instructions

1. **Clone/Download** the project files
2. **Start a local server**:
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Node.js (if you have http-server installed)
   npx http-server
   
   # PHP
   php -S localhost:8000
   ```
3. **Open browser** to `http://localhost:8000`

## Game Controls

### Desktop
- **Movement**: WASD or Arrow keys
- **Jump**: Spacebar
- **Close Game**: ESC key or X button

### Mobile
- **Movement**: On-screen D-pad
- **Jump**: Jump button
- **Close Game**: X button

## Customization

### Adding New Items
Edit `js/game.js` in the `generateGameItems()` method:

```javascript
const itemTypes = {
    treasures: [
        { name: 'New Treasure', emoji: '💎', count: 1 }
    ],
    // ... other categories
};
```

### Changing Colors
Modify CSS variables in `css/style.css`:

```css
:root {
    --pirate-brown: #8B4513;
    --pirate-blue: #1e3a5f;
    --pirate-gold: #FFD700;
    /* ... other colors */
}
```

### Hotel Booking Links
Update `js/script.js` in the `handleHotelClick()` function:

```javascript
const hotelUrls = {
    'baranof': 'https://your-booking-system.com',
    'southeast': 'https://southeastresort.com',
    'sitka': 'https://sitkahotel.com'
};
```

## Contact Information

**Baranof Lodge**
- Phone: (*************
- Email: <EMAIL>
- Address: 404 Sawmill Creek Rd, Sitka, AK 99835

## License

© 2025 by Baranof Lodge. All rights reserved.

## Development Notes

- Character sprite sheet: 18 frames at 32x32 pixels
- Map images: Landscape (desktop) and Portrait (mobile) versions
- Tile system: 32x32 pixel grid for precise item placement
- Performance: Optimized for smooth 60fps gameplay
- Browser Support: Modern browsers (Chrome, Firefox, Safari, Edge)

## Future Enhancements

- Online leaderboard with server backend
- Additional game levels/maps
- Sound effects and background music
- Multiplayer functionality
- Achievement system
- Social media sharing
