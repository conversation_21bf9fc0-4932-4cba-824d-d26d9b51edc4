/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Pirate Theme Colors */
    --pirate-brown: #8B4513;
    --pirate-dark-brown: #654321;
    --pirate-blue: #1e3a5f;
    --pirate-dark-blue: #0f1f3a;
    --pirate-gold: #FFD700;
    --pirate-cream: #F5E6D3;
    --pirate-red: #8B0000;
    --pirate-black: #2C1810;

    /* Transparency variants */
    --pirate-brown-alpha: rgba(139, 69, 19, 0.9);
    --pirate-blue-alpha: rgba(30, 58, 95, 0.9);
    --pirate-black-alpha: rgba(44, 24, 16, 0.8);
}

body {
    font-family: 'Roboto Slab', Georgia, 'Times New Roman', Times, serif;
    line-height: 1.6;
    background-color: var(--pirate-cream);
    color: var(--pirate-black);
    overflow-x: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'Cinzel', serif;
    font-weight: 700;
    color: var(--pirate-brown);
}

/* ===== NAVIGATION ===== */
.pirate-nav {
    background: linear-gradient(135deg, var(--pirate-brown-alpha), var(--pirate-blue-alpha));
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--pirate-gold);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    padding: 0.5rem 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-family: 'Cinzel', serif;
    font-weight: 700;
    color: var(--pirate-gold) !important;
    text-decoration: none;
}

.navbar-brand .logo {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    border-radius: 50%;
    border: 2px solid var(--pirate-gold);
}

.brand-text {
    font-size: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.navbar-nav .nav-link {
    color: var(--pirate-cream) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--pirate-gold) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

#bookNowNav {
    background: linear-gradient(45deg, var(--pirate-gold), #FFA500);
    color: var(--pirate-black) !important;
    border: none;
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

#bookNowNav:hover {
    background: linear-gradient(45deg, #FFA500, var(--pirate-gold));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* ===== HERO SECTION ===== */
.hero-section {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/images/hero/map_landscape.webp');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: -1;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
            rgba(139, 69, 19, 0.3),
            rgba(30, 58, 95, 0.3));
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: 2rem;
    background: var(--pirate-black-alpha);
    border-radius: 20px;
    border: 3px solid var(--pirate-gold);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.hero-title {
    font-size: 4rem;
    color: var(--pirate-gold);
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
    margin-bottom: 1rem;
    letter-spacing: 2px;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--pirate-cream);
    margin-bottom: 2rem;
    font-style: italic;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
}

/* ===== CALL TO ACTION BUTTONS ===== */
.cta-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-glow {
    background: linear-gradient(45deg, var(--pirate-gold), #FFA500);
    color: var(--pirate-black);
    border: none;
    border-radius: 15px;
    padding: 1rem 2rem;
    font-family: 'Cinzel', serif;
    font-weight: 700;
    font-size: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow:
        0 0 20px rgba(255, 215, 0, 0.5),
        0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-glow:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 0 30px rgba(255, 215, 0, 0.8),
        0 8px 25px rgba(0, 0, 0, 0.4);
}

.btn-glow::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.btn-glow:hover::before {
    animation: shine 0.6s ease-in-out;
}

@keyframes shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

.btn-adventure {
    background: linear-gradient(45deg, var(--pirate-brown), var(--pirate-blue));
    color: var(--pirate-cream);
    border: 2px solid var(--pirate-gold);
    border-radius: 15px;
    padding: 1rem 2rem;
    font-family: 'Cinzel', serif;
    font-weight: 600;
    font-size: 1.3rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-adventure:hover {
    background: linear-gradient(45deg, var(--pirate-blue), var(--pirate-brown));
    color: var(--pirate-gold);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

/* ===== FOOTER ===== */
.pirate-footer {
    background: linear-gradient(135deg, var(--pirate-black), var(--pirate-dark-brown));
    color: var(--pirate-cream);
    padding: 3rem 0 1rem;
    margin-top: 5rem;
    border-top: 3px solid var(--pirate-gold);
    position: relative;
}

.pirate-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 69, 19, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.footer-title {
    font-family: 'Cinzel', serif;
    font-size: 2.5rem;
    color: var(--pirate-gold);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 1.5rem;
}

.footer-title .baranof {
    color: var(--pirate-gold);
}

.footer-title .lodge {
    color: var(--pirate-cream);
    font-size: 2rem;
}

.contact-info {
    font-size: 1.1rem;
    line-height: 2;
}

.contact-info a {
    color: var(--pirate-cream);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-info a:hover {
    color: var(--pirate-gold);
}

.contact-info i {
    color: var(--pirate-gold);
    margin-right: 0.5rem;
    width: 20px;
}

.hotel-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.hotel-link {
    color: var(--pirate-cream);
    text-decoration: none;
    padding: 0.8rem 1.5rem;
    background: linear-gradient(45deg, var(--pirate-brown), var(--pirate-blue));
    border: 2px solid var(--pirate-gold);
    border-radius: 10px;
    transition: all 0.3s ease;
    font-weight: 600;
    text-align: center;
}

.hotel-link:hover {
    background: linear-gradient(45deg, var(--pirate-blue), var(--pirate-brown));
    color: var(--pirate-gold);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.copyright {
    color: var(--pirate-cream);
    font-size: 0.9rem;
    opacity: 0.8;
    border-top: 1px solid var(--pirate-gold);
    padding-top: 1rem;
}

/* ===== UTILITY CLASSES ===== */
.hidden {
    display: none !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-background {
        background-image: url('/images/hero/map_portrait.webp');
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-content {
        margin: 1rem;
        padding: 1.5rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-glow,
    .btn-adventure {
        width: 100%;
        max-width: 300px;
        margin-bottom: 1rem;
    }

    .navbar-brand .brand-text {
        font-size: 1.2rem;
    }

    .footer-title {
        font-size: 2rem;
        text-align: center;
    }

    .footer-title .lodge {
        font-size: 1.5rem;
    }

    .contact-info {
        text-align: center;
        margin-bottom: 2rem;
    }

    .hotel-links {
        margin-top: 1rem;
    }
}