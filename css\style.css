/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {}

body {
    font-family: 'Roboto Slab', Georgia, 'Times New Roman', Times, serif;
    line-height: 1.6;
    background: rgba(255, 230, 0, 0.18);
    height: 1700px;
    background-image: url("/images/hero/map_portrait.webp");
    background-size: fit;
    background-position: center;
    background-repeat: no-repeat;
}

h1,

h2,

h3,

h4,

h5,

h6 {
    font-family: 'Cin<PERSON>', serif;
    font-weight: 700;
}

.map {
    height: 1700px;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;

    /* filter: invert(100%) hue-rotate(180deg) brightness(95%) contrast(90%); */
}