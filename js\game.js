// ===== BARANOF LODGE PIRATE GAME ENGINE =====

class PirateGame {
    constructor() {
        this.isGameActive = false;
        this.score = 0;
        this.timeLeft = 120; // 2 minutes in seconds
        this.gameTimer = null;
        this.character = {
            x: 100,
            y: 100,
            width: 32,
            height: 32,
            speed: 4,
            isJumping: false,
            isWalking: false
        };
        
        // Game world dimensions (32x32 tile grid)
        this.tileSize = 32;
        this.worldWidth = 25; // tiles
        this.worldHeight = 19; // tiles
        
        // Game items and their positions
        this.gameItems = [];
        this.foundItems = [];
        
        // Input handling
        this.keys = {};
        this.isMobile = window.innerWidth <= 768;
        
        this.initializeGame();
    }
    
    initializeGame() {
        this.setupEventListeners();
        this.generateGameItems();
        this.updateFoundItemsList();
    }
    
    setupEventListeners() {
        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            if (this.isGameActive) {
                e.preventDefault();
            }
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
        
        // Mobile controls
        const mobileControls = document.querySelectorAll('.control-btn');
        mobileControls.forEach(btn => {
            btn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                const key = btn.dataset.key;
                this.keys[key] = true;
                btn.classList.add('active');
            });
            
            btn.addEventListener('touchend', (e) => {
                e.preventDefault();
                const key = btn.dataset.key;
                this.keys[key] = false;
                btn.classList.remove('active');
            });
        });
        
        // Game buttons
        document.getElementById('playGameBtn').addEventListener('click', () => {
            this.startGame();
        });
        
        document.getElementById('closeGameBtn').addEventListener('click', () => {
            this.endGame();
        });
        
        document.getElementById('playAgainBtn').addEventListener('click', () => {
            this.resetGame();
            this.startGame();
        });
    }
    
    generateGameItems() {
        this.gameItems = [];
        
        // Define item types and their properties
        const itemTypes = {
            // Treasures (worth 100 points each)
            treasures: [
                { name: 'Footsteps', emoji: '👣', count: 1 },
                { name: 'Shovel', emoji: '⛏️', count: 3 },
                { name: 'Skull and Bones', emoji: '💀', count: 1 },
                { name: 'Small House', emoji: '🏠', count: 1 },
                { name: 'Campfire', emoji: '🔥', count: 3 },
                { name: 'Church Cross', emoji: '✝️', count: 1 },
                { name: 'Fish', emoji: '🐟', count: 1 },
                { name: 'Bigfoot Foot', emoji: '🦶', count: 1 },
                { name: 'Hidden Cross', emoji: '⛪', count: 1 }
            ],
            // Trash (worth 50 points each)
            trash: [
                { name: 'Ziplock Bag', emoji: '🛍️', count: 2 },
                { name: 'Bottle', emoji: '🍾', count: 3 },
                { name: 'Face Mask', emoji: '😷', count: 2 }
            ],
            // Other items (worth 75 points each)
            other: [
                { name: 'Dragon', emoji: '🐉', count: 2 },
                { name: 'Ship', emoji: '🚢', count: 3 },
                { name: 'Plane', emoji: '✈️', count: 1 },
                { name: 'Forest', emoji: '🌲', count: 2 },
                { name: 'Tree', emoji: '🌳', count: 3 },
                { name: 'Camp Tent', emoji: '⛺', count: 2 },
                { name: 'Coffee Stain', emoji: '☕', count: 1 }
            ]
        };
        
        // Generate items with random positions
        let itemId = 0;
        
        Object.entries(itemTypes).forEach(([category, items]) => {
            items.forEach(itemType => {
                for (let i = 0; i < itemType.count; i++) {
                    const item = {
                        id: itemId++,
                        name: itemType.name,
                        emoji: itemType.emoji,
                        category: category,
                        x: Math.random() * (this.worldWidth - 2) + 1, // Keep away from edges
                        y: Math.random() * (this.worldHeight - 2) + 1,
                        found: false,
                        points: category === 'treasures' ? 100 : category === 'trash' ? 50 : 75
                    };
                    this.gameItems.push(item);
                }
            });
        });
        
        // Add hotel locations (special items)
        const hotels = [
            { name: 'Baranof Lodge', x: 12, y: 9, url: '#' },
            { name: 'Southeast Resort', x: 8, y: 15, url: '#' },
            { name: 'Sitka Hotel', x: 18, y: 6, url: '#' }
        ];
        
        hotels.forEach(hotel => {
            this.gameItems.push({
                id: itemId++,
                name: hotel.name,
                emoji: '🏨',
                category: 'hotel',
                x: hotel.x,
                y: hotel.y,
                found: false,
                points: 0,
                url: hotel.url
            });
        });
    }
    
    startGame() {
        this.isGameActive = true;
        this.score = 0;
        this.timeLeft = 120;
        this.foundItems = [];
        
        // Reset character position
        this.character.x = 100;
        this.character.y = 100;
        
        // Reset all items
        this.gameItems.forEach(item => item.found = false);
        
        // Show game container
        document.getElementById('gameContainer').classList.remove('hidden');
        document.getElementById('hero').style.display = 'none';
        
        // Hide game over modal if visible
        const modal = bootstrap.Modal.getInstance(document.getElementById('gameOverModal'));
        if (modal) {
            modal.hide();
        }
        
        // Start game loop
        this.gameLoop();
        this.startTimer();
        this.renderItems();
        this.updateUI();
    }
    
    gameLoop() {
        if (!this.isGameActive) return;
        
        this.handleInput();
        this.updateCharacter();
        this.checkCollisions();
        this.updateUI();
        
        requestAnimationFrame(() => this.gameLoop());
    }
    
    handleInput() {
        const character = this.character;
        let isMoving = false;
        
        // Movement controls (WASD or Arrow keys)
        if (this.keys['KeyW'] || this.keys['ArrowUp']) {
            character.y = Math.max(0, character.y - character.speed);
            isMoving = true;
        }
        if (this.keys['KeyS'] || this.keys['ArrowDown']) {
            character.y = Math.min(window.innerHeight - character.height, character.y + character.speed);
            isMoving = true;
        }
        if (this.keys['KeyA'] || this.keys['ArrowLeft']) {
            character.x = Math.max(0, character.x - character.speed);
            isMoving = true;
        }
        if (this.keys['KeyD'] || this.keys['ArrowRight']) {
            character.x = Math.min(window.innerWidth - character.width, character.x + character.speed);
            isMoving = true;
        }
        
        // Jump control (Space)
        if (this.keys['Space'] && !character.isJumping) {
            this.jump();
        }
        
        // Update character animation state
        character.isWalking = isMoving;
    }
    
    updateCharacter() {
        const characterElement = document.getElementById('character');
        characterElement.style.left = this.character.x + 'px';
        characterElement.style.top = this.character.y + 'px';
        
        // Update animation classes
        if (this.character.isWalking) {
            characterElement.classList.add('walking');
        } else {
            characterElement.classList.remove('walking');
        }
    }
    
    jump() {
        if (this.character.isJumping) return;
        
        this.character.isJumping = true;
        const characterElement = document.getElementById('character');
        characterElement.classList.add('jumping');
        
        setTimeout(() => {
            this.character.isJumping = false;
            characterElement.classList.remove('jumping');
        }, 500);
    }
    
    checkCollisions() {
        const gameWorld = document.getElementById('gameWorld');
        const worldRect = gameWorld.getBoundingClientRect();
        
        this.gameItems.forEach(item => {
            if (item.found) return;
            
            // Convert tile position to screen position
            const itemScreenX = (item.x * this.tileSize) + worldRect.left;
            const itemScreenY = (item.y * this.tileSize) + worldRect.top;
            
            // Check if character is within 15px of item
            const distance = Math.sqrt(
                Math.pow(this.character.x - (itemScreenX - worldRect.left), 2) +
                Math.pow(this.character.y - (itemScreenY - worldRect.top), 2)
            );
            
            if (distance <= 15) {
                this.collectItem(item);
            }
        });
    }
    
    collectItem(item) {
        if (item.found) return;
        
        item.found = true;
        this.foundItems.push(item);
        this.score += item.points;
        
        // Handle hotel clicks
        if (item.category === 'hotel' && item.url !== '#') {
            window.open(item.url, '_blank');
        }
        
        // Show score popup
        this.showScorePopup(item.points, this.character.x, this.character.y);
        
        // Update UI
        this.updateFoundItemsList();
        this.updateUI();
        
        // Check win condition
        const collectibleItems = this.gameItems.filter(item => item.category !== 'hotel');
        const foundCollectibleItems = this.foundItems.filter(item => item.category !== 'hotel');
        
        if (foundCollectibleItems.length >= collectibleItems.length) {
            this.winGame();
        }
    }
    
    showScorePopup(points, x, y) {
        const popup = document.createElement('div');
        popup.className = 'score-popup';
        popup.textContent = `+${points}`;
        popup.style.left = x + 'px';
        popup.style.top = y + 'px';
        
        document.getElementById('gameWorld').appendChild(popup);
        
        setTimeout(() => {
            popup.remove();
        }, 1500);
    }
    
    renderItems() {
        const gameItemsContainer = document.getElementById('gameItems');
        gameItemsContainer.innerHTML = '';
        
        this.gameItems.forEach(item => {
            if (item.found) return;
            
            const itemElement = document.createElement('div');
            itemElement.className = `game-item ${item.category}`;
            itemElement.style.left = (item.x * this.tileSize) + 'px';
            itemElement.style.top = (item.y * this.tileSize) + 'px';
            itemElement.textContent = item.emoji;
            itemElement.title = item.name;
            
            gameItemsContainer.appendChild(itemElement);
        });
    }
    
    updateFoundItemsList() {
        const list = document.getElementById('foundItemsList');
        list.innerHTML = '';
        
        // Group items by category
        const categories = ['treasures', 'trash', 'other'];
        
        categories.forEach(category => {
            const categoryItems = this.gameItems.filter(item => item.category === category);
            
            categoryItems.forEach(item => {
                const li = document.createElement('li');
                li.className = item.category;
                li.textContent = item.name;
                
                if (item.found) {
                    li.classList.add('found');
                }
                
                list.appendChild(li);
            });
        });
    }
    
    updateUI() {
        // Update score with gas pump style counting
        const scoreElement = document.getElementById('scoreValue');
        const currentScore = parseInt(scoreElement.textContent) || 0;
        
        if (currentScore !== this.score) {
            const increment = Math.ceil((this.score - currentScore) / 10);
            const newScore = Math.min(currentScore + increment, this.score);
            scoreElement.textContent = newScore.toString().padStart(6, '0');
            
            if (newScore < this.score) {
                setTimeout(() => this.updateUI(), 50);
            }
        }
        
        // Update timer
        const minutes = Math.floor(this.timeLeft / 60);
        const seconds = this.timeLeft % 60;
        document.getElementById('timerValue').textContent = 
            `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    
    startTimer() {
        this.gameTimer = setInterval(() => {
            this.timeLeft--;
            
            if (this.timeLeft <= 0) {
                this.timeUp();
            }
        }, 1000);
    }
    
    timeUp() {
        this.endGame();
        this.showGameOverModal('Time\'s Up!', 'You ran out of time, but you still found some treasures!');
    }
    
    winGame() {
        this.endGame();
        this.showGameOverModal('Congratulations!', 'You found all the treasures! You\'re a true pirate!');
    }
    
    endGame() {
        this.isGameActive = false;
        
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
            this.gameTimer = null;
        }
        
        // Hide game container
        document.getElementById('gameContainer').classList.add('hidden');
        document.getElementById('hero').style.display = 'block';
    }
    
    showGameOverModal(title, message) {
        document.getElementById('gameOverTitle').textContent = title;
        document.getElementById('gameOverMessage').textContent = message;
        document.getElementById('finalScore').textContent = this.score;
        
        this.updateLeaderboard();
        
        const modal = new bootstrap.Modal(document.getElementById('gameOverModal'));
        modal.show();
    }
    
    updateLeaderboard() {
        // Get existing scores from localStorage
        let scores = JSON.parse(localStorage.getItem('pirateGameScores') || '[]');
        
        // Display current leaderboard
        const list = document.getElementById('leaderboardList');
        list.innerHTML = '';
        
        scores.sort((a, b) => b.score - a.score);
        scores.slice(0, 5).forEach((entry, index) => {
            const li = document.createElement('li');
            li.textContent = `${entry.name}: ${entry.score}`;
            list.appendChild(li);
        });
        
        // Handle name submission
        const nameInput = document.getElementById('playerName');
        nameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.saveScore();
            }
        });
    }
    
    saveScore() {
        const nameInput = document.getElementById('playerName');
        const playerName = nameInput.value.trim();
        
        if (!playerName) return;
        
        let scores = JSON.parse(localStorage.getItem('pirateGameScores') || '[]');
        scores.push({
            name: playerName,
            score: this.score,
            date: new Date().toISOString()
        });
        
        localStorage.setItem('pirateGameScores', JSON.stringify(scores));
        this.updateLeaderboard();
        nameInput.value = '';
    }
    
    resetGame() {
        this.score = 0;
        this.timeLeft = 120;
        this.foundItems = [];
        this.character.x = 100;
        this.character.y = 100;
        
        this.gameItems.forEach(item => item.found = false);
        this.updateFoundItemsList();
        this.updateUI();
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.pirateGame = new PirateGame();
});
