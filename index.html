<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="index, follow">
    <!-- MARK: SEO -->
    <meta name="description"
        content="Affordable lodge rooms in Sitka, Alaska. Perfect for quick visits and extended stays. Conveniently located near local attractions and downtown, it's a comfortable, budget-friendly base for exploring Sitka.">
    <meta name="keywords"
        content="Sitka Alaska lodging, affordable rooms Sitka, long-term stay Sitka, budget friendly Alaska accommodation">
    <!-- OG: META tags -->
    <meta property="og:title" content="Baranof Lodge – Sitka, Alaska">
    <meta property="og:description"
        content="Affordable, comfortable lodging in the heart of Sitka, Alaska. Lodge rooms and apartments for travelers and professionals.">
    <meta property="og:image" content="https://baranovlodge.com/images/logo.png">
    <meta name="theme-color" content="#8B4513">
    <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Lodge",
          "name": "Baranof Lodge",
          "description": "Affordable lodge rooms in Sitka, Alaska. Perfect for quick visits and extended stays. Conveniently located near local attractions and downtown, it's a comfortable, budget-friendly base for exploring Sitka.",
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "US",
            "streetAddress": "404 Sawmill Creek Rd",
            "addressLocality": "Sitka",
            "addressRegion": "Alaska",
            "postalCode": "99835"
          },
          "url": "https://baranovlodge.com/",
          "telephone": "************",
          "image": "https://baranovlodge.com/images/logo.png",
          "priceRange": "$",
          "hasMap": "https://www.google.com/maps/dir/?api=1&destination=404+Sawmill+Creek+Rd+Sitka+Alaska+99835",
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": 57.0531,
            "longitude": -135.3338
          },
          "checkinTime": "15:00",
          "checkoutTime": "11:00"
        }
    </script>
    <!-- favicon -->
    <link rel="icon" type="image/png" sizes="48x48" href="/images/logo/logo2_48x48.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/logo2_180x180.png">
    <link rel="icon" href="/images/logo/logo2_48x48.png">
    <link rel="canonical" href="https://baranovlodge.com/">
    <title>Baranof Lodge | Comfortable, Affordable Lodging in Sitka, Alaska</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400..900&family=Roboto+Slab:wght@100..900&display=swap"
        rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css?ver=1.0">
    <link rel="stylesheet" href="css/game.css?ver=1.0">
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top pirate-nav">
        <div class="container">
            <a class="navbar-brand" href="#">
                <img src="/images/logo/logo2_250x250.webp" alt="Baranof Lodge" class="logo">
                <span class="brand-text">Baranof Lodge</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link btn btn-primary me-2" href="#" id="bookNowNav">Book Now</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tel:************">📞 (*************</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="mailto:<EMAIL>">Concerns?</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="hero-section">
        <div class="hero-background">
            <div class="hero-content">
                <h1 class="hero-title">Baranof Lodge</h1>
                <p class="hero-subtitle">Sitka, Alaska • Adventure Awaits</p>

                <div class="cta-buttons" id="ctaButtons">
                    <button class="btn btn-glow btn-lg me-3 mb-3" id="bookNowBtn">
                        <span class="glow-text">Book Now!</span>
                    </button>
                    <button class="btn btn-adventure btn-lg mb-3" id="playGameBtn">
                        <span>⚔️ Play Game</span>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Game Container -->
    <div id="gameContainer" class="game-container hidden">
        <div class="game-header">
            <div class="game-stats">
                <div class="score-display">
                    <span class="label">Score:</span>
                    <span id="scoreValue" class="value">000000</span>
                </div>
                <div class="timer-display">
                    <span class="label">Time:</span>
                    <span id="timerValue" class="value">2:00</span>
                </div>
            </div>
            <button class="btn btn-close-game" id="closeGameBtn">✕</button>
        </div>

        <div class="game-world" id="gameWorld">
            <canvas id="gameCanvas" width="800" height="600"></canvas>
            <div id="character" class="character"></div>
            <div id="gameItems" class="game-items"></div>
        </div>

        <div class="game-ui">
            <div class="found-items-panel">
                <h4>Found Items</h4>
                <ul id="foundItemsList" class="found-items-list"></ul>
            </div>

            <!-- Mobile Controls -->
            <div class="mobile-controls" id="mobileControls">
                <div class="dpad">
                    <button class="control-btn up" data-key="ArrowUp">↑</button>
                    <div class="middle-row">
                        <button class="control-btn left" data-key="ArrowLeft">←</button>
                        <button class="control-btn right" data-key="ArrowRight">→</button>
                    </div>
                    <button class="control-btn down" data-key="ArrowDown">↓</button>
                </div>
                <button class="control-btn jump" data-key="Space">Jump</button>
            </div>
        </div>
    </div>

    <!-- Game Over Modal -->
    <div id="gameOverModal" class="modal fade" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content pirate-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="gameOverTitle">Game Complete!</h5>
                </div>
                <div class="modal-body text-center">
                    <p id="gameOverMessage">Congratulations! You found all the treasures!</p>
                    <p>Final Score: <span id="finalScore" class="score-highlight">0</span></p>
                    <div class="name-input-section">
                        <label for="playerName">Enter your name for the leaderboard:</label>
                        <input type="text" id="playerName" class="form-control mt-2" maxlength="20"
                            placeholder="Your Name">
                    </div>
                    <div id="leaderboard" class="leaderboard mt-4">
                        <h6>High Scores</h6>
                        <ol id="leaderboardList"></ol>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-adventure" id="playAgainBtn">Play Again</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer id="contact" class="pirate-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h3 class="footer-title">
                        <span class="baranof">Baranof</span><br>
                        <span class="lodge">Lodge</span>
                    </h3>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i><a href="tel:************">(*************</a></p>
                        <p><i class="fas fa-envelope"></i><a
                                href="mailto:<EMAIL>"><EMAIL></a></p>
                        <p><i class="fas fa-map-marker-alt"></i><a target="_blank"
                                href="https://www.google.com/maps/dir/?api=1&destination=404+Sawmill+Creek+Rd+Sitka+Alaska+99835">404
                                Sawmill Creek Rd, Sitka, AK 99835</a></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <h4>Sister Properties</h4>
                    <div class="hotel-links">
                        <a href="#" class="hotel-link" data-hotel="baranof">Baranof Lodge</a>
                        <a href="#" class="hotel-link" data-hotel="southeast">Southeast Resort</a>
                        <a href="#" class="hotel-link" data-hotel="sitka">Sitka Hotel</a>
                    </div>
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-12 text-center">
                    <p class="copyright">&copy; 2025 by Baranof Lodge. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/game.js"></script>
    <script src="js/script.js"></script>
</body>

</html>